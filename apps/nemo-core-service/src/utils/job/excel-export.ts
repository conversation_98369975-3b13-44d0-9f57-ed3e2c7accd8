import { JobShippingStatus, JobStatus, QCStatus } from '../../../src/entities';

import { AllJobStatus, ProductStatus } from '../../../src/config';

export type IExcelTableType = 'ALL_STATUS_JOB'; // future

/** IExcelTableSlug
 * - merge all IExcelTableSlug... to one type */
export type IExcelTableSlug =
  | IExcelTableSlugAllJobStatus
  | IExcelTableSlugExampleOthSlugs;

/** IExcelTableSlug for ALL_STATUS_JOB type */
export type IExcelTableSlugAllJobStatus =
  | 'jobId'
  | 'deviceKey'
  | 'deviceKey2'
  | 'createdBy'
  | 'createdAt'
  | 'updatedBy'
  | 'updatedAt'
  | 'requestedAt'
  | 'assignedAt'
  | 'estimatedAt'
  | 'purchasedAt'
  | 'rejectedAt'
  | 'completedShopAt'
  | 'status'
  | 'rom'
  | 'brand'
  | 'model'
  | 'modelKey'
  | 'matCode'
  | 'suggestedPrice'
  | 'currentGrade'
  | 'estimatedGrade'
  | 'shippingStatus'
  | 'receivedAt'
  | 'qcBy'
  | 'qcAt'
  | 'qcStatus'
  | 'repairedBy'
  | 'repairedAt'
  | 'assignRepairAt'
  | 'inspectedBy'
  | 'inspectedAt'
  | 'assignInspectAt'
  | 'branchId'
  | 'costPrice'
  | 'retailPrice'
  | 'retailMargin'
  | 'wholeSalePrice'
  | 'wholeSaleMargin'
  | 'deviceColor'
  | 'marginRetailBaht'
  | 'marginWholeSaleBaht'
  | 'voucherId';

export const slugLabelAllJobStatus: Record<
  IExcelTableSlugAllJobStatus,
  string
> = {
  jobId: 'Transaction ID',
  deviceKey: 'เลข IMEI',
  deviceKey2: 'เลข IMEI 2',
  createdBy: 'พนักงานรับซื้อ',
  createdAt: 'วันเวลาที่รับซื้อ',
  updatedBy: 'ผู้ทำรายการล่าสุด',
  updatedAt: 'วันเวลาที่อัปเดตล่าสุด',
  requestedAt: 'วันเวลาที่ขอประเมินราคา',
  assignedAt: 'วันเวลาที่รับงานประเมินราคา',
  estimatedAt: 'วันเวลาที่ประเมินราคาสินค้า',
  purchasedAt: 'วันเวลาที่ซื้อขายทำสัญญาสำเร็จ',
  rejectedAt: 'วันเวลาที่ยกเลิกหรือปฏิเสธการซื้อขาย',
  completedShopAt: 'วันเวลาที่ทำการขายหรือยกเลิกสำเร็จ',
  status: 'สถานะ',
  rom: 'ความจุ',
  brand: 'ยี่ห้อ',
  model: 'รุ่น',
  modelKey: 'รุ่น TradeFone',
  matCode: 'Material',
  suggestedPrice: 'ราคารับซื้อ',
  currentGrade: 'เกรด',
  estimatedGrade: 'เกรด',
  shippingStatus: 'สถานะการจัดส่ง',
  receivedAt: 'วันเวลาที่รับเครื่อง',
  qcBy: 'QC โดย',
  qcAt: 'วันเวลาที่ QC',
  qcStatus: 'สถานะ QC',
  repairedBy: 'Repaired โดย',
  repairedAt: 'วันเวลาที่ Repair',
  assignRepairAt: 'วันเวลาที่รับงาน Repair',
  inspectedBy: 'Inspection โดย',
  inspectedAt: 'วันเวลาที่ Inspection',
  assignInspectAt: 'วันเวลาที่รับงาน Inspection',
  branchId: 'สาขา',
  costPrice: 'ราคาต้นทุน',
  retailPrice: 'ราคาขายปลีก',
  retailMargin: 'Margin ขายปลีก (%)',
  wholeSalePrice: 'ราคาขายส่ง',
  wholeSaleMargin: 'Margin ขายส่ง (%)',
  deviceColor: 'สี',
  marginRetailBaht: 'Margin ขายปลีก (บาท)',
  marginWholeSaleBaht: 'Margin ขายส่ง (บาท)',
  voucherId: 'Trade-in Voucher (เลขที่ คูปอง)',
};

export type IExcelTableSlugExampleOthSlugs = 'xxx';

export const shippingStatusCodeMapping: { [key in string]: string } = {
  [JobShippingStatus.RECEIVED]: 'รับสินค้าแล้ว',
  [JobShippingStatus.RECEIVED_OTHER]: 'รับสินค้าแบบอื่นๆ',
  [JobShippingStatus.RECEIVED_WITH_CONDITION]: 'รับสินค้าแบบมีเงื่อนไข',
  [JobShippingStatus.SHIPPED]: 'ยังไม่ได้รับสินค้า',
};

export const qcStatusCodeMapping: { [key in string]: string } = {
  [QCStatus.FIX]: 'ส่งซ่อม',
  [QCStatus.SCRAP]: 'ส่งขายซาก',
  [QCStatus.REFURBISH]: 'ส่ง Refurbish',
};

export const getProductStatusFromCodeStatus = (
  status: string,
  qcStatus: string,
) => {
  switch (status) {
    case JobStatus.INSPECTION_AUTO_COMPLETED:
      return ProductStatus.READY;
    case JobStatus.INSPECTION_COMPLETED:
      return qcStatus === QCStatus.SCRAP
        ? ProductStatus.SCARP
        : ProductStatus.READY;
  }
};

export const getJobsStatusFromCodeStatus = (
  status: string = '',
  qcStatus?: string,
) => {
  switch (status) {
    case JobStatus.DRAFT:
      return AllJobStatus.SHOP_WAITING_PROCESS;
    case JobStatus.QUOTE_REQUESTED:
      return AllJobStatus.SHOP_WAITING_PROCESS;
    case JobStatus.ESTIMATE_PRICE_PROCESSING:
      return AllJobStatus.AFS_ESTIMATING;
    case JobStatus.PRICE_ESTIMATED:
      return AllJobStatus.SHOP_ESTIMATE_SUCCESS;
    case JobStatus.IDENTITY_REQUESTED:
      return AllJobStatus.SHOP_WAITING_APPROVE;
    case JobStatus.IDENTITY_REJECTED:
      return AllJobStatus.SHOP_NOT_APPROVE;
    case JobStatus.IDENTITY_VERIFIED:
      return AllJobStatus.SHOP_APPROVE_SUCCESS;
    case JobStatus.PURCHASED:
      return AllJobStatus.SHOP_PURCHASED;
    case JobStatus.RECEIVED:
      return AllJobStatus.AFS_QC;
    case JobStatus.QC_COMPLETED:
      return qcStatus === QCStatus.SCRAP
        ? AllJobStatus.AFS_INSPECTION
        : AllJobStatus.AFS_REPAIR;
    case JobStatus.REPAIR_ASSIGNED:
      return qcStatus === QCStatus.SCRAP
        ? AllJobStatus.AFS_INSPECTION
        : AllJobStatus.AFS_REPAIR;
    case JobStatus.REPAIR_COMPLETED:
      return AllJobStatus.AFS_INSPECTION;
    case JobStatus.INSPECTION_ASSIGNED:
      return AllJobStatus.AFS_INSPECTION;
    case JobStatus.INSPECTION_FAILED:
      return AllJobStatus.AFS_REPAIR;
    case JobStatus.INSPECTION_COMPLETED:
      return AllJobStatus.SCM_READY;
    case JobStatus.INSPECTION_AUTO_COMPLETED:
      return AllJobStatus.SCM_READY;
    case JobStatus.REJECT_BY_CUSTOMER:
      return AllJobStatus.SHOP_CANCEL;
    case JobStatus.REJECT_BY_SHOP:
      return AllJobStatus.SHOP_CANCEL;
    case JobStatus.AO_CREATED:
      return AllJobStatus.AO_CREATED;
    case JobStatus.AO_RECEIVED:
      return AllJobStatus.AO_RECEIVED;
    case JobStatus.CAMPAIGN_SELECTED:
      return AllJobStatus.SHOP_WAITING_SIGNED;
    default:
      return '-';
  }
};

export const jobUserColumn = [
  { entityKey: 'createdBy', dbKey: 'r_created_by' },
  { entityKey: 'updatedBy', dbKey: 'r_updated_by' },
  { entityKey: 'qcBy', dbKey: 'r_qc_by' },
  { entityKey: 'repairedBy', dbKey: 'r_repaired_by' },
  { entityKey: 'inspectedBy', dbKey: 'r_inspected_by' },
  { entityKey: 'shopUserKey', dbKey: 'r_shop_user_key' },
  { entityKey: 'adminUserKey', dbKey: 'r_admin_user_key' },
  { entityKey: 'receiverUserKey', dbKey: 'r_receiver_user_key' },
];

export const jobDateTimeColumn = [
  { entityKey: 'createdAt', dbKey: 'r_created_at' },
  { entityKey: 'updatedAt', dbKey: 'r_updated_at' },
  { entityKey: 'requestedAt', dbKey: 'r_requested_at' },
  { entityKey: 'assignedAt', dbKey: 'r_assigned_at' },
  { entityKey: 'estimatedAt', dbKey: 'r_estimated_at' },
  { entityKey: 'purchasedAt', dbKey: 'r_purchased_at' },
  { entityKey: 'rejectedAt', dbKey: 'r_rejected_at' },
  { entityKey: 'completedShopAt', dbKey: 'r_completed_shop_at' },
  { entityKey: 'receivedAt', dbKey: 'r_received_at' },
  { entityKey: 'qcAt', dbKey: 'r_qc_at' },
  { entityKey: 'repairedAt', dbKey: 'r_repaired_at' },
  { entityKey: 'inspectedAt', dbKey: 'r_inspected_at' },
  { entityKey: 'assignInspectAt', dbKey: 'r_assign_inspect_at' },
  { entityKey: 'assignRepairAt', dbKey: 'r_assign_repair_at' },
];

export const jobAllStatusColumn = [
  { entityKey: 'status', dbKey: 'r_status' },
  { entityKey: 'shippingStatus', dbKey: 'r_shipping_status' },
  { entityKey: 'qcStatus', dbKey: 'r_qc_status' },
];

export const jobMapDatColumn = [
  { entityKey: 'companyId', dbKey: 'r_company_id' },
  { entityKey: 'jobId', dbKey: 'r_job_id' },
  { entityKey: 'deviceKey', dbKey: 'r_device_key' },
  { entityKey: 'deviceKey2', dbKey: 'r_device_key2' },
  { entityKey: 'modelKey', dbKey: 'r_model_key' },
  { entityKey: 'thaiId', dbKey: 'r_thai_id' },
  { entityKey: 'currentGrade', dbKey: 'r_current_grade' },
  { entityKey: 'shopUserName', dbKey: 'r_shop_user_name' },
  { entityKey: 'adminUserName', dbKey: 'r_admin_user_name' },
  { entityKey: 'deliveryOrderId', dbKey: 'r_delivery_order_id' },
  { entityKey: 'receivingRemark', dbKey: 'r_receiving_remark' },
  { entityKey: 'estimatedGrade', dbKey: 'r_estimated_grade' },
  { entityKey: 'costPrice', dbKey: 'r_cost_price' },
  { entityKey: 'retailPrice', dbKey: 'r_retail_price' },
  { entityKey: 'wholeSalePrice', dbKey: 'r_whole_sale_price' },
  { entityKey: 'retailMargin', dbKey: 'r_retail_margin' },
  { entityKey: 'wholeSaleMargin', dbKey: 'r_whole_sale_margin' },
  { entityKey: 'status', dbKey: 'r_status' },
  { entityKey: 'shippingStatus', dbKey: 'r_shipping_status' },
  { entityKey: 'qcStatus', dbKey: 'r_qc_status' },
  { entityKey: 'marginWholeSaleBaht', dbKey: 'r_margin_whole_sale_baht' },
  { entityKey: 'marginRetailBaht', dbKey: 'r_margin_retail_baht' },
  { entityKey: 'vendorType', dbKey: 'r_vendor_type' },
];
