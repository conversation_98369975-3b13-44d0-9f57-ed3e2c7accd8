import { FC, useEffect, useMemo, useState } from 'react'
import { Button, DropdownSimple } from 'ui'
// import Tooltip from '@mui/material/Tooltip'
import { Tooltip, TooltipProps } from '@material-tailwind/react'
import { offset } from '@material-tailwind/react/types/components/popover'
import 'survey-core/defaultV2.min.css'

import { useTranslation } from '@/hooks'
import { ModelMasterGrade } from '@/services/models/modelMaster'
import { formatPrice, getIsCancelStatus, defaultValue } from '@/utils'
import { useUserStore } from '@/stores/userStore'

export interface EstimatePrice {
  grade: string
  minPrice: number
  maxPrice: number
}

const offsetTooltip: { [key: string]: offset } = {
  top: { mainAxis: 5 },
  'top-end': { crossAxis: 5, mainAxis: 5 },
}

const PriceFooterTooltip: FC<TooltipProps> = ({ children, content, open, placement = 'top' }) => {
  return (
    <Tooltip
      open={open}
      placement={placement}
      offset={offsetTooltip[placement]}
      className={`border border-base-700 bg-base-700 rounded-lg text-center tooltip-${placement}`}
      content={content}
    >
      {children}
    </Tooltip>
  )
}

const getGrade = (price: number, gradeList: ModelMasterGrade[] | undefined): EstimatePrice => {
  if (price <= 0 || !gradeList) {
    return {
      grade: '-',
      maxPrice: Number(1),
      minPrice: Number(0),
    }
  }
  const modelMasterGrades = gradeList.sort((g1, g2) => Number(g2.purchasePrice) - Number(g1.purchasePrice))

  for (let i = 0; i < modelMasterGrades.length; i++) {
    if (
      price >= Number(modelMasterGrades[i].purchasePrice) ||
      price >= Number(modelMasterGrades[i + 1]?.purchasePrice || '1') + 1
    ) {
      return {
        grade: modelMasterGrades[i].grade,
        maxPrice: Number(modelMasterGrades[i].purchasePrice),
        minPrice: Number(defaultValue(modelMasterGrades[i + 1]?.purchasePrice, '0')) + 1,
      }
    }
  }

  const lastGrade = modelMasterGrades.find(modelMaster => modelMaster.grade === 'D')
  return {
    grade: defaultValue(lastGrade?.grade, 'D'),
    minPrice: 1,
    maxPrice: Number(defaultValue(lastGrade?.purchasePrice, '1')),
  }
}

type State = 'pending' | 'owner' | 'not-owner' | 'estimated' | 'cancelled' | undefined
/**
 * pending - display grade and price range
 * owner - display grade, price range and editable price
 * not-owner - display grade and price range
 * estimated - display price only
 * undefined - and dont have dataAtMount will display "No data to display"
 * if have dataAtMount but state is undefined will calculate state from dataAtMount
 */

export interface Props {
  footerState?: State
  dataAtMount: any
  calculatedPrice: number
  tick?: number
  onSubmit?: (price: number, grade: string) => void
  getPrice?: (price: number, isManual: boolean) => void
  permissionEdit: boolean
}

export const PriceFooter: FC<Props> = ({
  footerState,
  dataAtMount,
  calculatedPrice,
  tick = 100,
  onSubmit,
  getPrice,
  permissionEdit,
}) => {
  const { t } = useTranslation('common')
  const { user } = useUserStore()
  const [price, setPrice] = useState<number>(calculatedPrice && calculatedPrice > 0 ? calculatedPrice : 0)
  const [gradeDetail, setGradeDetail] = useState<EstimatePrice>()
  const [isClickTick, setIsClickTick] = useState<boolean>(false)
  const [disablePlus, setDisablePlus] = useState<boolean>(true)
  const [disableMinus, setDisableMinus] = useState<boolean>(true)
  const [selectedGrade, setSelectedGrade] = useState<ModelMasterGrade | null>(null)

  const state: State = useMemo(() => {
    if (footerState) {
      return footerState
    }
    if (user?.userKey && dataAtMount) {
      if (!getIsCancelStatus(dataAtMount.status)) {
        if (!dataAtMount.adminUserKey) {
          return 'pending'
        } else if (dataAtMount.suggestedPrice == 0) {
          return dataAtMount.adminUserKey === user.userKey && permissionEdit ? 'owner' : 'not-owner'
        } else if (dataAtMount.suggestedPrice > 0) {
          return 'estimated'
        }
      }
      return dataAtMount.suggestedPrice == 0 ? 'cancelled' : 'estimated'
    }
    return undefined
  }, [user, dataAtMount, permissionEdit])

  const [gradeList, maxMinPrice, isKingfisher, gradeChoices] = useMemo(() => {
    let maxMin = { max: 1, min: 1 }
    let isKingfisher = false
    let gradeChoices: { value: string; text: string; data: ModelMasterGrade }[] = []

    if (dataAtMount) {
      const gradeList = dataAtMount.modelTemplate?.modelMasterGrades as ModelMasterGrade[]
      isKingfisher = dataAtMount.vendorType === 'KINGFISHER'

      if (gradeList) {
        const modelMasterGrades = gradeList.sort((g1, g2) => Number(g2.purchasePrice) - Number(g1.purchasePrice))
        maxMin = { max: Number(modelMasterGrades[0].purchasePrice || '1'), min: 1 }

        // Create grade choices for dropdown
        if (isKingfisher) {
          gradeChoices = modelMasterGrades.map(grade => ({
            value: grade.grade,
            text: `${grade.grade} - ${formatPrice(Number(grade.purchasePrice), 2)}`,
            data: grade,
          }))
        }
      }
      return [gradeList, maxMin, isKingfisher, gradeChoices]
    }
    return [undefined, maxMin, isKingfisher, gradeChoices]
  }, [dataAtMount])

  useEffect(() => {
    if (!isClickTick) {
      setPrice(calculatedPrice)
    }
  }, [calculatedPrice])

  useEffect(() => {
    if (state === 'owner' && dataAtMount?.suggestedPriceKeep) {
      setIsClickTick(true)
      setPrice(dataAtMount.suggestedPriceKeep)
    } else {
      setIsClickTick(false)
    }
  }, [dataAtMount])

  useEffect(() => {
    if (!gradeDetail?.grade || gradeDetail?.grade === '-') return
    if (isKingfisher && gradeChoices.length > 0 && !selectedGrade && gradeDetail?.grade) {
      const gradeData = gradeChoices.find(choice => choice.value === gradeDetail?.grade)?.data
      if (gradeData) {
        console.log('1')
        setSelectedGrade(gradeData)
      }
    }
  }, [isKingfisher, gradeChoices, selectedGrade, gradeDetail?.grade])

  useEffect(() => {
    const { max, min } = maxMinPrice
    if (price + tick > max) {
      setDisablePlus(true)
    } else {
      setDisablePlus(false)
    }
    if (price - tick < min) {
      setDisableMinus(true)
    } else {
      setDisableMinus(false)
    }
    if (
      !gradeDetail ||
      (gradeDetail && (price > gradeDetail.maxPrice || price < gradeDetail.minPrice)) ||
      gradeDetail.grade === '-'
    ) {
      setGradeDetail(getGrade(price, gradeList))
    }
    if (getPrice) {
      getPrice(price, isClickTick)
    }
  }, [price, gradeList, isClickTick])

  return state && dataAtMount ? (
    <footer className="bg-white flex-col w-[100%]">
      <div className="flex flex-row items-end justify-between px-6 pt-4 pb-6">
        <span className="text-h4-bold text-primary-500">{t('jobs-management.estimate-product-price')}</span>
        <div className="flex">
          {/* grade part */}
          {state !== 'estimated' && (
            <div className="flex flex-col mr-4 justify-end">
              <span className="text-b4-regular text-base-500">{t('jobs-management.grade')}</span>
              {isKingfisher && state === 'owner' ? (
                <div className="min-w-[120px]">
                  <DropdownSimple
                    selected={
                      selectedGrade
                        ? { value: selectedGrade.grade, text: selectedGrade.grade }
                        : { value: '', text: '' }
                    }
                    setSelection={(item: any) => {
                      const gradeData = gradeChoices.find(choice => choice.value === item.value)?.data
                      if (gradeData) {
                        setSelectedGrade(gradeData)
                        setPrice(Number(gradeData.purchasePrice))
                        setIsClickTick(true)
                      }
                    }}
                    choices={gradeChoices}
                    valueField="value"
                    textField="value"
                    t={t}
                    absoluteChoice={true}
                    autoPosition={true}
                  />
                </div>
              ) : (
                <span className="text-t3-semi-bold text-base-700">{gradeDetail?.grade}</span>
              )}
            </div>
          )}
          {/* price range part */}
          {state !== 'estimated' && (
            <div
              className={`flex flex-col ${
                ['pending', 'cancelled', 'not-owner'].includes(state)
                  ? //  || !permissionEdit
                    ''
                  : 'mr-10'
              } justify-end`}
            >
              <span className="text-b4-regular text-base-500">{t('jobs-management.min-max-price')}</span>
              <span className="text-t3-semi-bold text-base-700">
                {isKingfisher && selectedGrade
                  ? formatPrice(Number(selectedGrade.purchasePrice), 2)
                  : gradeDetail && gradeDetail.grade != '-'
                    ? `${formatPrice(gradeDetail?.minPrice ?? 0, 2)} - ${formatPrice(gradeDetail?.maxPrice ?? 0, 2)}`
                    : '-'}
              </span>
            </div>
          )}
          <div className="flex flex-row">
            {/* price and edit price part */}
            {!['pending', 'cancelled', 'not-owner'].includes(state) && (
              <div className="flex flex-col text-right mr-2 items-end justify-end gap-[2px]">
                {isClickTick && state === 'owner' && (
                  <PriceFooterTooltip
                    content={<div dangerouslySetInnerHTML={{ __html: t('common.jobs-detail.hover-reset') }}></div>}
                  >
                    <div
                      className="flex flex-row gap-[2px] items-center text-info-500 text-b4-regular cursor-pointer"
                      onClick={() => {
                        setIsClickTick(false)
                        setPrice(calculatedPrice)
                      }}
                    >
                      <i className="icons !w-4 !h-4 ic-restart-ligth-blue"></i>
                      {t('common.jobs-management.reset')}
                    </div>
                  </PriceFooterTooltip>
                )}
                <div className="flex flex-row gap-[2px] items-center text-primary-500 text-t4-semi-bold">
                  {state === 'owner' && (
                    <PriceFooterTooltip
                      content={
                        <div
                          dangerouslySetInnerHTML={{
                            __html: t('common.jobs-detail.hover-buying-price', { tick: formatPrice(tick) }),
                          }}
                        ></div>
                      }
                    >
                      <i className="icons !w-4 !h-4 ic-info-gray"></i>
                    </PriceFooterTooltip>
                  )}
                  {t('common.jobs-management.buying-price')}
                </div>
              </div>
            )}
            {state === 'owner' && (
              <div className="text-h2-bold flex flex-row border border-primary-500 rounded-lg items-center gap-3 p-[9px]">
                <PriceFooterTooltip
                  open={disableMinus ? undefined : false}
                  content={
                    <div dangerouslySetInnerHTML={{ __html: t('common.jobs-detail.hover-cant-decrease-price') }}></div>
                  }
                >
                  <i
                    className={`icons ${disableMinus ? 'ic-minus-gray' : 'ic-minus-blue'} cursor-pointer`}
                    onClick={() => {
                      if (!disableMinus) {
                        setPrice(price - tick)
                        setIsClickTick(true)
                      }
                    }}
                  ></i>
                </PriceFooterTooltip>
                <div>{formatPrice(price, 2)}</div>
                <PriceFooterTooltip
                  open={disablePlus ? undefined : false}
                  placement="top-end"
                  content={
                    <div dangerouslySetInnerHTML={{ __html: t('common.jobs-detail.hover-cant-increase-price') }}></div>
                  }
                >
                  <i
                    className={`icons  ${disablePlus ? 'ic-add-gray' : 'ic-add-blue'} cursor-pointer`}
                    onClick={() => {
                      if (!disablePlus || price == 0) {
                        setPrice(price + tick)
                        setIsClickTick(true)
                      }
                    }}
                  ></i>
                </PriceFooterTooltip>
              </div>
            )}
            {state == 'estimated' && (
              <div className="text-h2-bold flex flex-row items-end">{formatPrice(price, 2)}</div>
            )}
            {/* end price and edit price part */}
          </div>
        </div>
      </div>
      {/* submit button part */}
      {state === 'owner' && (
        <div className={`flex justify-end pt-[0.9rem] pb-6 px-6 border-t`}>
          <Button
            colorScheme="primary"
            variant="filled"
            size="m"
            cls={`h-[48px] w-[168px]`}
            isDisabled={price <= 0 || (isKingfisher && !selectedGrade)}
            onClick={() => {
              if (onSubmit) {
                const grade = isKingfisher && selectedGrade ? selectedGrade.grade : gradeDetail?.grade || ''
                onSubmit(price, grade)
              }
            }}
          >
            {t('confirm')}
          </Button>
        </div>
      )}
    </footer>
  ) : (
    <div>No data to display</div>
  )
}
