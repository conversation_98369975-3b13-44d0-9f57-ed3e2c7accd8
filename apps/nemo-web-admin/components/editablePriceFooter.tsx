import { FC, useEffect, useMemo, useState } from 'react'
import { Button, DropdownSimple } from 'ui'

import { useTranslation } from '@/hooks'
import { ModelMasterGrade } from '@/services/models/modelMaster'
import { formatPrice } from '@/utils'

export interface IEditablePriceFooterProps {
  dataAtMount: any
  calculatedPrice: number
  onSubmit?: (price: number, grade: string) => void
}

export const EditablePriceFooter: FC<IEditablePriceFooterProps> = ({ dataAtMount, calculatedPrice, onSubmit }) => {
  const { t } = useTranslation('common')

  const [price, setPrice] = useState<number>(calculatedPrice && calculatedPrice > 0 ? calculatedPrice : 0)
  const [selectedGrade, setSelectedGrade] = useState<ModelMasterGrade | null>(null)

  // Check if this is a Kingfisher job and create grade choices
  const [isKingfisher, gradeChoices] = useMemo(() => {
    const isKingfisher = dataAtMount?.vendorType === 'KINGFISHER'
    let gradeChoices: { value: string; text: string; data: ModelMasterGrade }[] = []

    if (isKingfisher && dataAtMount?.modelTemplate?.modelMasterGrades) {
      const gradeList = dataAtMount.modelTemplate.modelMasterGrades as ModelMasterGrade[]
      gradeChoices = gradeList.map(grade => ({
        value: grade.grade,
        text: `${grade.grade} - ${formatPrice(Number(grade.purchasePrice), 2)}`,
        data: grade,
      }))
    }

    return [isKingfisher, gradeChoices]
  }, [dataAtMount])

  // Initialize selected grade for Kingfisher
  useEffect(() => {
    if (isKingfisher && gradeChoices.length > 0 && !selectedGrade) {
      setSelectedGrade(gradeChoices[0].data)
      setPrice(Number(gradeChoices[0].data.purchasePrice))
    }
  }, [isKingfisher, gradeChoices, selectedGrade])

  return (
    <div className="bg-white border-t border-base-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {/* Grade selection for Kingfisher */}
          {isKingfisher && (
            <div className="flex flex-col mr-4 justify-end">
              <span className="text-b4-regular text-base-500">{t('jobs-management.grade')}</span>
              <div className="min-w-[120px]">
                <DropdownSimple
                  selected={
                    selectedGrade ? { value: selectedGrade.grade, text: selectedGrade.grade } : { value: '', text: '' }
                  }
                  setSelection={(item: any) => {
                    const gradeData = gradeChoices.find(choice => choice.value === item.value)?.data
                    if (gradeData) {
                      setSelectedGrade(gradeData)
                      setPrice(Number(gradeData.purchasePrice))
                    }
                  }}
                  choices={gradeChoices}
                  valueField="value"
                  textField="value"
                  t={t}
                  absoluteChoice={true}
                  autoPosition={true}
                />
              </div>
            </div>
          )}

          {/* Price display */}
          <div className="flex flex-col mr-4 justify-end">
            <span className="text-b4-regular text-base-500">{t('jobs-management.price')}</span>
            <span className="text-t3-semi-bold text-base-700">{formatPrice(price, 2)}</span>
          </div>
        </div>

        {/* Submit button */}
        <div className="flex items-center space-x-2">
          <Button
            variant="filled"
            size="m"
            colorScheme="primary"
            onClick={() => {
              if (onSubmit && selectedGrade) {
                onSubmit(price, selectedGrade.grade)
              }
            }}
            isDisabled={!selectedGrade || price <= 0}
          >
            {t('common.submit')}
          </Button>
        </div>
      </div>
    </div>
  )
}
