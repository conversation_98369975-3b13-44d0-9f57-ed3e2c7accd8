export interface GetJobsRequest {
  deviceKey?: string
  status?: string[]
  excludeStatus?: string[]
  isAvailableForDo?: string
  search?: string
  orderBy?: string
  page?: string
  pageSize?: string
  isExcludeUser?: string
  isExactSearch?: string
}

export interface GetJobsHistory {
  deviceKey?: string
  page?: string
  pageSize?: string
}
export interface GetJobsRequestAdmin {
  deviceKey?: string
  status?: string[]
  shippingStatus?: string[]
  search?: string
  orderBy?: string
  page?: string
  pageSize?: string
  myJob?: string
  partialReceived?: string
}

export interface CreateJobDraftSignBody {
  estimationId: string
  contractKey: string
}
export interface CreateJobDraftBody {
  modelKey: string
  deviceKey: string
  depositContractKey: string
}

export interface NewCreateJobBody {
  estimationId: string
  deviceKey: string
  deviceKey2: string
  phoneNumber: string
  [key: string]: any
}

export interface CreateJobBody {
  jobId: string
  checkList: any[]
  checkListValues: any
}

export interface ImageJobRequest {
  id: string
  key: string
}

export interface CommentJobBody {
  message: string
}

export interface IdentityRejectedBody {
  reason: string
}

export interface ConfirmContractBody {
  contractKey: string
}

export interface UpdateCustomerInfoJobBody {
  photo: string
  identificationNumber: string
  thaiName: {
    firstName: string
    lastName: string
    middleName: string
    title: string
  }
  engName: {
    firstName: string
    lastName: string
    middleName: string
    title: string
  }
  address: {
    subdistrict: {
      code: string
      name: string
    }
    district: {
      code: string
      name: string
    }
    province: {
      code: string
      name: string
    }
    moo: string
    houseNumber: string
    trok: string
    soi: string
    road: string
  }
  birthDate: string
  issueDate: string
  expireDate: string
  mobileNumber: string
  email: string
  rejectReason?: string
  isOcr?: boolean
}

export interface GetJobsResponse {
  companyId?: string
  jobId?: string
  deviceKey?: string
  branchId?: string
  modelKey?: string
  thaiId?: string
  createdBy?: string
  updatedBy?: string
  status?: string
  vendorType?: string
  modelIdentifiers?: any
  modelTemplate?: any
  checkList?: any
  checkListValues?: any
  suggestedPrice?: number
  purchasedPrice?: number
  createdAt?: Date
  updatedAt?: Date
  branch?: any
  adminUserKey?: string
  adminUser?: any
  requestedAt?: Date
  assignedAt?: Date
  estimatedAt?: Date
  purchasedAt?: Date
  completedShopAt?: Date
  rejectedAt?: Date
  deliveryOrderId?: string
  deliveryOrder?: any
  shippingStatus?: string
  receiverUserKey?: string
  receivingRemark?: string
  receivedAt?: Date
  qcBy?: string
  qcAt?: Date
  qcUser?: any
  qcStatus?: string
  adminCheckListValues?: any
  repairedBy?: any
  repairedAt?: Date
  assignRepairAt?: Date
  repairListValue?: any
  repairedUser?: any
  currentGrade?: string
  inspectedBy?: any
  assignInspectAt?: Date
  inspectedAt?: Date
  inspectedUser?: any
  updatedUser?: any
  costPrice?: number
  retailPrice?: number
  wholeSalePrice?: number
  retailMargin?: number
  wholeSaleMargin?: number
  marginRetailBaht?: number
  marginWholeSaleBaht?: number
  allocationOrderId?: string
  aoShippingStatus?: string
  aoReceivedAt?: Date
  isConfirmPrice?: boolean
  allocationOrder?: any
}

export interface IGetJobWithRelationParamObj {
  relations: string[]
  type?: string
}

export interface SignContractBody {
  signature: string
}
export interface SignDepositContractBody {
  signature?: { sign: string; firstName: string; lastName: string }
}

export interface SignContractResponse {
  signedContract: string
  contractKey: string
}

export interface ReceiveJobsBody {
  id: string
  shippingStatus: string
  remark: string | null
}
export interface ReceiveJobs {
  jobs: ReceiveJobsBody[]
}

export interface QCStatusBody {
  status: string
}

export type IConfirmRepairJobBody = {
  detail: string
  cost?: number
  grade?: string
  type: 'confirm' | 'refurbish' | 'scrap'
}

export type IMonthlyInspectionBody = {
  month: number
  year: number
}

export interface IMonthlyInspection {
  date: Date
  allProduct: number
  aGradeProduct: number
  otherGradeProduct: number
  scrapProduct: number
  completeStatus: boolean
}

export interface AssignRepairBody {
  qcStatus?: string
}
export type InspectResult = 'pass' | 'fail'

export interface InspectJobBody {
  inspectionDetail?: string
  inspectionResult: InspectResult
}

export interface ProductBody {
  retailPrice: number
  wholeSalePrice: number
}

export interface ISubmitConfirmPriceItem {
  jobId: string
  updatedAt: Date
}

export interface SubmitConfirmPriceBody {
  jobs: ISubmitConfirmPriceItem[]
}

// for dto
export interface ISubmitConfirmPriceItemDto {
  jobId: string
  updatedAt: string
}

export interface SubmitConfirmPriceBodyDto {
  jobs: ISubmitConfirmPriceItemDto[]
}

export interface ISubmitAOIncompleteBody {
  remark?: string
  videoPath?: string
}

export interface IPostOcrBody {
  image: string
  key: string
  excludeOcr?: boolean
}
export interface IGetMediaUrlBody {
  path: string
}

// ** Activity type for decision collection path firestore **
export enum ActivitiesType {
  // Path: `company/${entity.companyId}/conversation/${entity.jobId}/activity`;
  CONVERSATION = 'conversation',

  // Path: `company/${entity.companyId}/inbox/${entity.detail.branchId}/message`;
  INBOX = 'inbox',

  // Path: `company/${entity.companyId}/voucher-conversation/${entity.voucherId}/activity`;
  VOUCHER_CONVERSATION = 'voucher_conversation',

  // Path: `company/${entity.companyId}/do-conversation/${entity.deliveryOrderId}/activity`;
  DO_CONVERSATION = 'do_conversation',
}

// ** Inbox type for represent type of inbox to ui **
export enum InboxType {
  // Admin comment
  ADMIN_FEEDBACK = 'admin_feedback',

  // Shop comment
  SHOP_FEEDBACK = 'shop_feedback',

  // Job create
  JOB_CREATE = 'job_create',

  // Job assigned
  ESTIMATE_PROCESSING = 'estimate_processing',

  // Job estimated
  ESTIMATE_COMPLETE = 'estimate_complete',

  // Identity requested
  IDENTITY_REQUESTED = 'identity_request',

  // Identity verified
  IDENTITY_VERIFIED = 'identity_verify',

  // Identity rejected
  IDENTITY_REJECTED = 'identity_reject',

  // Voucher requested
  VOUCHER_REQUESTED = 'voucher_requested',

  // Delivery order requested
  DELIVERY_ORDER_REQUESTED = 'delivery_order_requested',

  // Delivery order shipped
  DELIVERY_ORDER_SHIPPED = 'delivery_order_shipped',

  // Delivery order confirmed
  DELIVERY_ORDER_CONFIRMED = 'delivery_order_confirmed',

  // Delivery order completed
  DELIVERY_ORDER_SUCCCESS = 'delivery_order_success',

  // Imported Voucher notificaiton
  IMPORTED_VOUCHER_OUT_OF_STOCK = 'imported_voucher_out_of_stock',
  // Issue report approved
  ISSUE_REPORT_APPROVED = 'issue_report_approved',

  // Issue report rejected
  ISSUE_REPORT_REJECTED = 'issue_report_rejected',

  // Issue report requested
  ISSUE_REPORT_REQUESTED = 'issue_report_requested',
}

// ** Audience type for decision inbox subscriber firestore frontend **
export enum AudienceType {
  // All shop in branch ex. shop_all_branch
  SHOP_BRANCH = 'shop_all_branch',

  // Role manager only
  MANAGER_ONLY = 'manager_only',

  // Employee only ex. employee_only:{userKey}
  EMPLOYEE_ONLY = 'employee_only',

  // Price estimator only ex. price_estimator:{userKey}
  PRICE_ESTIMATOR = 'price_estimator',

  // Admin B
  RECEIVE = 'receive',

  // Admin Marketing
  ADMIN_MARKETING = 'admin_marketing',

  // Admin RCC
  RCC = 'rcc',

  // All role
  ALL = 'all',
}

export interface CalculatedMargin {
  retailPrice: number | null
  wholeSalePrice: number | null
  retailMargin: number | null
  marginRetailBaht: number | null
  wholeSaleMargin: number | null
  marginWholeSaleBaht: number | null
  costPrice: number
}
